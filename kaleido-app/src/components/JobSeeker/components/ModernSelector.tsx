'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, ChevronDown, X, Plus, Search } from 'lucide-react';

interface Option {
  value: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
}

interface ModernSelectorProps {
  label: string;
  options: Option[];
  selectedValues: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
  required?: boolean;
  multiple?: boolean;
  searchable?: boolean;
  maxSelections?: number;
  error?: string;
  hint?: string;
  className?: string;
}

export const ModernSelector: React.FC<ModernSelectorProps> = ({
  label,
  options,
  selectedValues,
  onChange,
  placeholder = 'Select options...',
  required = false,
  multiple = true,
  searchable = false,
  maxSelections,
  error,
  hint,
  className = '',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredOptions = searchable
    ? options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        option.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  const selectedOptions = options.filter(option => selectedValues.includes(option.value));

  const handleSelect = (option: Option) => {
    if (multiple) {
      if (selectedValues.includes(option.value)) {
        onChange(selectedValues.filter(value => value !== option.value));
      } else {
        if (maxSelections && selectedValues.length >= maxSelections) {
          return;
        }
        onChange([...selectedValues, option.value]);
      }
    } else {
      onChange([option.value]);
      setIsOpen(false);
    }
  };

  const removeSelection = (value: string, e?: React.MouseEvent) => {
    e?.stopPropagation();
    onChange(selectedValues.filter(v => v !== value));
  };

  return (
    <div className={`relative ${className}`}>
      {/* Label */}
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
        {maxSelections && (
          <span className="text-gray-400 ml-2 text-xs">
            ({selectedValues.length}/{maxSelections})
          </span>
        )}
      </label>

      {/* Selector Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={`w-full min-h-[48px] px-3 py-2 border rounded-lg text-left transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 ${
          error
            ? 'border-red-500 bg-red-50'
            : isOpen
            ? 'border-purple-500 ring-2 ring-purple-100'
            : 'border-gray-300 hover:border-gray-400 bg-white'
        }`}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            {selectedOptions.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {selectedOptions.slice(0, 3).map((option) => (
                  <span
                    key={option.value}
                    className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-700 rounded-md text-sm"
                  >
                    {option.icon}
                    <span>{option.label}</span>
                    {multiple && (
                      <button
                        onClick={(e) => removeSelection(option.value, e)}
                        className="ml-1 hover:bg-purple-200 rounded-full p-0.5 transition-colors"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    )}
                  </span>
                ))}
                {selectedOptions.length > 3 && (
                  <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-md text-sm">
                    +{selectedOptions.length - 3} more
                  </span>
                )}
              </div>
            ) : (
              <span className="text-gray-400">{placeholder}</span>
            )}
          </div>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-gray-400" />
          </motion.div>
        </div>
      </button>

      {/* Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.15 }}
            className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-64 overflow-hidden"
          >
            {/* Search */}
            {searchable && (
              <div className="p-3 border-b border-gray-100">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search options..."
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:border-purple-500"
                  />
                </div>
              </div>
            )}

            {/* Options */}
            <div className="max-h-48 overflow-y-auto">
              {filteredOptions.length > 0 ? (
                filteredOptions.map((option) => {
                  const isSelected = selectedValues.includes(option.value);
                  const isDisabled = maxSelections && !isSelected && selectedValues.length >= maxSelections;

                  return (
                    <button
                      key={option.value}
                      onClick={() => !isDisabled && handleSelect(option)}
                      disabled={isDisabled}
                      className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center gap-3 ${
                        isSelected ? 'bg-purple-50 text-purple-900' : 'text-gray-900'
                      } ${
                        isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                      }`}
                    >
                      <div className="flex-shrink-0">
                        {isSelected ? (
                          <div className="w-5 h-5 bg-purple-500 rounded flex items-center justify-center">
                            <Check className="w-3 h-3 text-white" />
                          </div>
                        ) : (
                          <div className="w-5 h-5 border-2 border-gray-300 rounded" />
                        )}
                      </div>
                      <div className="flex items-center gap-2 flex-1">
                        {option.icon}
                        <div>
                          <div className="font-medium">{option.label}</div>
                          {option.description && (
                            <div className="text-sm text-gray-500">{option.description}</div>
                          )}
                        </div>
                      </div>
                    </button>
                  );
                })
              ) : (
                <div className="px-4 py-3 text-center text-gray-500">
                  {searchTerm ? 'No options found' : 'No options available'}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Messages */}
      {(error || hint) && (
        <div className="mt-2">
          {error ? (
            <p className="text-sm text-red-600">{error}</p>
          ) : hint ? (
            <p className="text-sm text-gray-500">{hint}</p>
          ) : null}
        </div>
      )}
    </div>
  );
};

export const ModernRangeSelector: React.FC<{
  label: string;
  minValue: number;
  maxValue: number;
  onMinChange: (value: number) => void;
  onMaxChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  prefix?: string;
  suffix?: string;
  required?: boolean;
  error?: string;
  hint?: string;
}> = ({
  label,
  minValue,
  maxValue,
  onMinChange,
  onMaxChange,
  min = 0,
  max = 1000000,
  step = 1000,
  prefix = '',
  suffix = '',
  required = false,
  error,
  hint,
}) => {
  const formatValue = (value: number) => {
    if (value >= 1000000) {
      return `${prefix}${(value / 1000000).toFixed(1)}M${suffix}`;
    }
    if (value >= 1000) {
      return `${prefix}${(value / 1000).toFixed(0)}K${suffix}`;
    }
    return `${prefix}${value.toLocaleString()}${suffix}`;
  };

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>{formatValue(minValue)}</span>
          <span>to</span>
          <span>{formatValue(maxValue)}</span>
        </div>
        
        <div className="relative">
          <input
            type="range"
            min={min}
            max={max}
            step={step}
            value={minValue}
            onChange={(e) => onMinChange(Number(e.target.value))}
            className="absolute w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-thumb"
          />
          <input
            type="range"
            min={min}
            max={max}
            step={step}
            value={maxValue}
            onChange={(e) => onMaxChange(Number(e.target.value))}
            className="absolute w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-thumb"
          />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-xs text-gray-500 mb-1">Minimum</label>
            <input
              type="number"
              value={minValue}
              onChange={(e) => onMinChange(Number(e.target.value))}
              min={min}
              max={maxValue}
              step={step}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:border-purple-500"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-500 mb-1">Maximum</label>
            <input
              type="number"
              value={maxValue}
              onChange={(e) => onMaxChange(Number(e.target.value))}
              min={minValue}
              max={max}
              step={step}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:border-purple-500"
            />
          </div>
        </div>
      </div>
      
      {(error || hint) && (
        <div className="mt-2">
          {error ? (
            <p className="text-sm text-red-600">{error}</p>
          ) : hint ? (
            <p className="text-sm text-gray-500">{hint}</p>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default ModernSelector;