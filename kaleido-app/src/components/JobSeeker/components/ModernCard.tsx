'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle2, AlertTriangle, Info, Sparkles } from 'lucide-react';

export type CardVariant = 'default' | 'success' | 'warning' | 'info' | 'gradient' | 'required';

interface ModernCardProps {
  children: React.ReactNode;
  variant?: CardVariant;
  title?: string;
  subtitle?: string;
  icon?: React.ReactNode;
  className?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
  required?: boolean;
  completed?: boolean;
  onToggle?: (expanded: boolean) => void;
}

const getVariantStyles = (variant: CardVariant, completed?: boolean) => {
  switch (variant) {
    case 'success':
      return 'border-gray-200 bg-white shadow-sm';
    case 'warning':
      return 'border-gray-200 bg-white shadow-sm';
    case 'info':
      return 'border-gray-200 bg-white shadow-sm';
    case 'gradient':
      return 'border-gray-200 bg-white shadow-sm';
    case 'required':
      return completed 
        ? 'border-purple-200 bg-white shadow-sm'
        : 'border-amber-100 bg-white shadow-sm';
    default:
      return 'border-gray-200 bg-white shadow-sm';
  }
};

const getVariantIcon = (variant: CardVariant, completed?: boolean) => {
  switch (variant) {
    case 'success':
      return <CheckCircle2 className="w-5 h-5 text-gray-600" />;
    case 'warning':
      return <AlertTriangle className="w-5 h-5 text-gray-600" />;
    case 'info':
      return <Info className="w-5 h-5 text-gray-600" />;
    case 'gradient':
      return <Sparkles className="w-5 h-5 text-gray-600" />;
    case 'required':
      return completed
        ? <CheckCircle2 className="w-5 h-5 text-purple-400" />
        : <AlertTriangle className="w-5 h-5 text-amber-500" />;
    default:
      return null;
  }
};

export const ModernCard: React.FC<ModernCardProps> = ({
  children,
  variant = 'default',
  title,
  subtitle,
  icon,
  className = '',
  required = false,
  completed = false,
  ...props
}) => {
  const variantStyles = getVariantStyles(variant, completed);
  const defaultIcon = icon || getVariantIcon(variant, completed);

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className={`border rounded-xl shadow-sm hover:shadow-md transition-all duration-200 ${variantStyles} ${className}`}
    >
      {title && (
        <div className="px-4 py-3 border-b border-gray-100">
          <div className="flex items-center gap-2">
            <h3 className="text-sm font-semibold text-gray-900">{title}</h3>
            {required && !completed && (
              <span className="text-xs text-purple-400">required</span>
            )}
            {completed && (
              <CheckCircle2 className="w-3 h-3 text-purple-400" />
            )}
          </div>
          {subtitle && (
            <p className="text-xs text-gray-500 mt-0.5">{subtitle}</p>
          )}
        </div>
      )}
      <div className="p-4">
        {children}
      </div>
    </motion.div>
  );
};

export const CompactCard: React.FC<ModernCardProps> = ({
  children,
  variant = 'default',
  className = '',
  ...props
}) => {
  const variantStyles = getVariantStyles(variant, props.completed);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.15 }}
      className={`border rounded-xl shadow-sm hover:shadow-md transition-all duration-200 p-5 ${variantStyles} ${className}`}
    >
      {children}
    </motion.div>
  );
};

export const StatCard: React.FC<{
  value: string | number;
  label: string;
  sublabel?: string;
  trend?: 'up' | 'down' | 'neutral';
  color?: 'blue' | 'green' | 'purple' | 'amber';
}> = ({ value, label, sublabel, color = 'blue' }) => {
  const colorClasses = {
    blue: 'text-gray-700 bg-white border-gray-200',
    green: 'text-green-600 bg-white border-gray-200',
    purple: 'text-gray-700 bg-white border-gray-200',
    amber: 'text-amber-600 bg-white border-gray-200',
  };

  return (
    <CompactCard className={colorClasses[color]}>
      <div className="text-center">
        <div className="text-3xl font-extrabold tracking-tight">{value}</div>
        <div className="text-sm font-semibold mt-2 opacity-90">{label}</div>
        {sublabel && (
          <div className="text-xs opacity-70 mt-1 font-medium">{sublabel}</div>
        )}
      </div>
    </CompactCard>
  );
};

export default ModernCard;