import { Briefcase, CheckCircle, FileText, Target, User, XCircle, ChevronDown, ChevronUp } from 'lucide-react';
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

import styles from '../styles/progressBar.module.css';
import { IJobSeekerProfile } from '../types';
import { isFieldFromResume } from '../utils/resumeUtils';

interface ResumeImportSummaryProps {
  formData: IJobSeekerProfile;
  resumeData: any;
  onImportComplete?: (updatedData: IJobSeekerProfile) => void;
  jobSeekerId?: string;
}

const sections = {
  basicInfo: {
    title: 'Basic Information',
    icon: User,
    isRequired: true,
    fields: {
      firstName: { label: 'First Name', required: true },
      lastName: { label: 'Last Name', required: true },
      email: { label: 'Email', required: true },
      phone: { label: 'Phone Number', required: false },
      location: { label: 'Location', required: false },
    },
  },
  professionalInfo: {
    title: 'Professional Information',
    icon: Briefcase,
    isRequired: true,
    fields: {
      summary: { label: 'Professional Summary', required: true },
      skills: { label: 'Skills', required: true },
      experience: { label: 'Work Experience', required: true },
      education: { label: 'Education', required: true },
      certifications: { label: 'Certifications', required: false },
    },
  },
  preferences: {
    title: 'Job Preferences',
    icon: Target,
    isRequired: true,
    fields: {
      jobTypes: { label: 'Job Types', required: true },
      locations: { label: 'Preferred Locations', required: false },
      industries: { label: 'Industries', required: false },
      remotePreference: { label: 'Remote Work Preference', required: false },
    },
  },
  additionalInfo: {
    title: 'Additional Information',
    icon: FileText,
    isRequired: false,
    fields: {
      languages: { label: 'Languages', required: false },
      myValues: { label: 'Personal Values', required: false },
      portfolioUrl: { label: 'Portfolio URL', required: false },
      videoIntroUrl: { label: 'Video Introduction', required: false },
      resumeUrl: { label: 'Resume', required: false },
    },
  },
};

export const ResumeImportSummary: React.FC<ResumeImportSummaryProps> = ({
  formData,
  resumeData,
  onImportComplete,
  jobSeekerId,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  if (!resumeData) return null;

  const importedFields = Object.keys(sections).flatMap(sectionKey =>
    Object.keys(sections[sectionKey].fields).filter(field =>
      isFieldFromResume(field, formData, resumeData)
    )
  );

  const allFields = Object.keys(sections).flatMap(sectionKey =>
    Object.keys(sections[sectionKey].fields)
  );

  const missingFields = allFields.filter(field => !importedFields.includes(field));
  const completionPercentage = Math.round((importedFields.length / allFields.length) * 100);

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
      {/* Compact Header - Always Visible */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors rounded-t-xl"
      >
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
            <FileText className="w-5 h-5 text-white" />
          </div>
          <div className="text-left">
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-gray-900">Resume Import</h3>
              <div className="flex items-center gap-1 px-2 py-1 bg-green-100 rounded-full">
                <CheckCircle className="w-3 h-3 text-green-600" />
                <span className="text-xs font-medium text-green-700">{completionPercentage}%</span>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              {importedFields.length} of {allFields.length} fields imported
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <motion.div
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-gray-400" />
          </motion.div>
        </div>
      </button>

      {/* Collapsible Detailed View */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="border-t border-gray-100"
          >
            <div className="p-4 space-y-4">
              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="font-medium text-gray-700">Import Progress</span>
                  <span className="text-purple-600 font-semibold">{completionPercentage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-purple-600 to-purple-500 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${completionPercentage}%` }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                  />
                </div>
              </div>

              {/* Sections Grid - More Compact */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(sections).map(([sectionKey, section]) => {
                  const SectionIcon = section.icon;
                  const sectionFields = Object.entries(section.fields);
                  const importedInSection = sectionFields.filter(([field]) => importedFields.includes(field));
                  
                  return (
                    <div key={sectionKey} className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <SectionIcon className="w-4 h-4 text-purple-600" />
                          <h4 className="font-medium text-gray-900 text-sm">{section.title}</h4>
                        </div>
                        <div className="flex items-center gap-1">
                          <span className="text-xs px-2 py-1 rounded-full bg-purple-600 text-white">
                            {importedInSection.length}/{sectionFields.length}
                          </span>
                          {section.isRequired && (
                            <span className="text-xs px-2 py-1 rounded-full bg-red-100 text-red-600">
                              Required
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <div className="space-y-1">
                        {sectionFields.map(([field, fieldInfo]) => {
                          const isImported = importedFields.includes(field);
                          return (
                            <div key={field} className="flex items-center gap-2">
                              {isImported ? (
                                <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0" />
                              ) : (
                                <XCircle className="w-3 h-3 text-gray-400 flex-shrink-0" />
                              )}
                              <span className={`text-xs ${isImported ? 'text-green-700' : 'text-gray-500'}`}>
                                {fieldInfo.label}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Footer Note - More Subtle */}
              <div className="text-xs text-gray-500 bg-purple-50 p-3 rounded-lg">
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-purple-600" />
                  <span>
                    Imported from your resume file. Missing fields can be completed manually.
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
