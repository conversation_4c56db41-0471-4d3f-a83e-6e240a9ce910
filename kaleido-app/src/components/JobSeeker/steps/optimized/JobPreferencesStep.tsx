'use client';

import { motion } from 'framer-motion';
import { AlertCircle, CheckCircle, Info, Briefcase, MapPin, Home, DollarSign, Target, TrendingUp, Users, Sparkles, Clock, Award } from 'lucide-react';
import React from 'react';
import { BaseStepProps } from '../../types';
import { ModernCard, StatCard } from '../../components/ModernCard';
import { ModernSelector, ModernRangeSelector } from '../../components/ModernSelector';

// Common job types with descriptions
const JOB_TYPES = [
  { value: 'FULL_TIME', label: 'Full-time', description: 'Regular 40-hour work week', icon: <Briefcase className="w-4 h-4" /> },
  { value: 'PART_TIME', label: 'Part-time', description: 'Less than 40 hours per week', icon: <Briefcase className="w-4 h-4" /> },
  { value: 'CONTRACT', label: 'Contract', description: 'Project-based or temporary work', icon: <Briefcase className="w-4 h-4" /> },
  { value: 'FREELANCE', label: 'Freelance', description: 'Independent contractor work', icon: <Briefcase className="w-4 h-4" /> },
  { value: 'INTERNSHIP', label: 'Internship', description: 'Learning-focused positions', icon: <Briefcase className="w-4 h-4" /> },
];

const REMOTE_PREFERENCES = [
  { value: 'REMOTE_ONLY', label: 'Remote Only', description: 'Work from anywhere', icon: <Home className="w-4 h-4" /> },
  { value: 'HYBRID', label: 'Hybrid', description: 'Mix of remote and office', icon: <Home className="w-4 h-4" /> },
  { value: 'ON_SITE', label: 'On-site', description: 'Office-based work', icon: <MapPin className="w-4 h-4" /> },
  { value: 'FLEXIBLE', label: 'Flexible', description: 'Open to different arrangements', icon: <Home className="w-4 h-4" /> },
];

const POPULAR_LOCATIONS = [
  { value: 'new-york-ny', label: 'New York, NY', icon: <MapPin className="w-4 h-4" /> },
  { value: 'san-francisco-ca', label: 'San Francisco, CA', icon: <MapPin className="w-4 h-4" /> },
  { value: 'los-angeles-ca', label: 'Los Angeles, CA', icon: <MapPin className="w-4 h-4" /> },
  { value: 'chicago-il', label: 'Chicago, IL', icon: <MapPin className="w-4 h-4" /> },
  { value: 'boston-ma', label: 'Boston, MA', icon: <MapPin className="w-4 h-4" /> },
  { value: 'seattle-wa', label: 'Seattle, WA', icon: <MapPin className="w-4 h-4" /> },
  { value: 'austin-tx', label: 'Austin, TX', icon: <MapPin className="w-4 h-4" /> },
  { value: 'denver-co', label: 'Denver, CO', icon: <MapPin className="w-4 h-4" /> },
];

const JobPreferencesStep: React.FC<BaseStepProps> = ({ formData, onUpdate, onNext }) => {
  // Ensure preferences object exists
  const preferences = formData.preferences || {};
  
  // Check completion status
  const hasJobTypes = preferences.jobTypes?.length > 0;
  const hasLocations = preferences.locations?.length > 0;
  const hasRemotePreference = !!preferences.remotePreference;
  const hasSalaryRange = !!(
    preferences.desiredSalary?.min &&
    preferences.desiredSalary?.max &&
    preferences.desiredSalary?.currency &&
    preferences.desiredSalary?.period
  );

  const allPreferencesComplete = hasJobTypes && hasLocations && hasRemotePreference && hasSalaryRange;
  const completedFields = [hasJobTypes, hasLocations, hasRemotePreference, hasSalaryRange].filter(Boolean).length;

  const updatePreferences = (updates: any) => {
    onUpdate({
      preferences: {
        ...preferences,
        ...updates,
      },
    });
  };

  return (
    <div className="space-y-4">
      {/* Compact Progress Indicator */}
      <div className="flex items-center justify-between mb-2">
        <div className="text-xs text-gray-500">{completedFields}/4 preferences</div>
        {allPreferencesComplete && (
          <CheckCircle className="w-4 h-4 text-purple-400" />
        )}
      </div>

      {/* Job Types */}
      <ModernCard 
        variant={hasJobTypes ? 'success' : 'default'} 
        title="Job Types" 
        required
        completed={hasJobTypes}
      >
        <div className="space-y-3">

          <ModernSelector
            label="Select employment types"
            options={JOB_TYPES}
            selectedValues={preferences.jobTypes || []}
            onChange={(values) => updatePreferences({ jobTypes: values })}
            placeholder="Choose your preferred employment types..."
            required
            multiple
            maxSelections={3}
            hint="Select up to 3 types to maximize your job opportunities"
          />
        </div>
      </ModernCard>

      {/* Locations */}
      <ModernCard 
        variant={hasLocations ? 'success' : 'default'} 
        title="Target Locations" 
        subtitle="Where do you want to build your career?"
        required
        completed={hasLocations}
      >
        <div className="space-y-4">
          {/* Compact location benefits */}
          <div className="flex items-center gap-2 p-2.5 bg-gray-50 rounded-lg">
            <MapPin className="w-4 h-4 text-gray-600" />
            <span className="text-sm text-gray-700">Expands your job market by 3x</span>
          </div>

          <ModernSelector
            label="Select target locations"
            options={POPULAR_LOCATIONS}
            selectedValues={preferences.locations || []}
            onChange={(values) => updatePreferences({ locations: values })}
            placeholder="Choose cities or add custom locations..."
            required
            multiple
            searchable
            maxSelections={5}
            hint="Select up to 5 locations where you'd like to work"
          />
        </div>
      </ModernCard>

      {/* Remote Preference */}
      <ModernCard 
        variant={hasRemotePreference ? 'success' : 'default'} 
        title="Work Style Preference" 
        subtitle="What environment helps you thrive?"
        required
        completed={hasRemotePreference}
      >
        <div className="space-y-4">
          {/* Compact work style benefits */}
          <div className="flex items-center gap-2 p-2.5 bg-gray-50 rounded-lg">
            <Home className="w-4 h-4 text-gray-600" />
            <span className="text-sm text-gray-700">Match your lifestyle preferences</span>
          </div>

          <ModernSelector
            label="Preferred work arrangement"
            options={REMOTE_PREFERENCES}
            selectedValues={preferences.remotePreference ? [preferences.remotePreference] : []}
            onChange={(values) => updatePreferences({ remotePreference: values[0] })}
            placeholder="Choose your ideal work setup..."
            required
            multiple={false}
            hint="Match opportunities with your lifestyle and productivity preferences"
          />
        </div>
      </ModernCard>

      {/* Salary Range */}
      <ModernCard 
        variant={hasSalaryRange ? 'success' : 'default'} 
        title="Compensation Goals" 
        subtitle="Set your financial expectations"
        required
        completed={hasSalaryRange}
      >
        <div className="space-y-4">
          {/* Compact salary guidance */}
          <div className="flex items-center gap-2 p-2.5 bg-gray-50 rounded-lg">
            <DollarSign className="w-4 h-4 text-gray-600" />
            <span className="text-sm text-gray-700">Get roles that meet your financial goals</span>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
              <select
                value={preferences.desiredSalary?.currency || 'USD'}
                onChange={(e) => updatePreferences({
                  desiredSalary: {
                    ...preferences.desiredSalary,
                    currency: e.target.value,
                  },
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
              >
                <option value="USD">USD ($)</option>
                <option value="EUR">EUR (€)</option>
                <option value="GBP">GBP (£)</option>
                <option value="CAD">CAD (C$)</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Period</label>
              <select
                value={preferences.desiredSalary?.period || 'YEAR'}
                onChange={(e) => updatePreferences({
                  desiredSalary: {
                    ...preferences.desiredSalary,
                    period: e.target.value,
                  },
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
              >
                <option value="YEAR">Per Year</option>
                <option value="MONTH">Per Month</option>
                <option value="HOUR">Per Hour</option>
              </select>
            </div>
          </div>
          
          <ModernRangeSelector
            label="Compensation Range"
            minValue={preferences.desiredSalary?.min || 50000}
            maxValue={preferences.desiredSalary?.max || 150000}
            onMinChange={(value) => updatePreferences({
              desiredSalary: {
                ...preferences.desiredSalary,
                min: value,
              },
            })}
            onMaxChange={(value) => updatePreferences({
              desiredSalary: {
                ...preferences.desiredSalary,
                max: value,
              },
            })}
            min={20000}
            max={500000}
            step={5000}
            prefix="$"
            required
            hint="Set a realistic range that reflects your market value"
          />
        </div>
      </ModernCard>

      {/* Completion Status */}
      {!allPreferencesComplete ? (
        <ModernCard variant="warning">
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <Clock className="w-5 h-5 text-amber-600" />
              <div>
                <p className="font-semibold text-amber-800">Almost There!</p>
                <p className="text-sm text-amber-700 mt-1">
                  Complete all preferences to unlock personalized job matching
                </p>
              </div>
            </div>
            
            {/* Progress indicators */}
            <div className="grid grid-cols-2 gap-3">
              <div className={`flex items-center gap-2 p-2 rounded-lg ${hasJobTypes ? 'bg-green-100' : 'bg-gray-100'}`}>
                <div className={`w-4 h-4 rounded-full ${hasJobTypes ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                <span className={`text-sm ${hasJobTypes ? 'text-green-800' : 'text-gray-600'}`}>Job Types</span>
              </div>
              <div className={`flex items-center gap-2 p-2 rounded-lg ${hasLocations ? 'bg-green-100' : 'bg-gray-100'}`}>
                <div className={`w-4 h-4 rounded-full ${hasLocations ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                <span className={`text-sm ${hasLocations ? 'text-green-800' : 'text-gray-600'}`}>Locations</span>
              </div>
              <div className={`flex items-center gap-2 p-2 rounded-lg ${hasRemotePreference ? 'bg-green-100' : 'bg-gray-100'}`}>
                <div className={`w-4 h-4 rounded-full ${hasRemotePreference ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                <span className={`text-sm ${hasRemotePreference ? 'text-green-800' : 'text-gray-600'}`}>Work Style</span>
              </div>
              <div className={`flex items-center gap-2 p-2 rounded-lg ${hasSalaryRange ? 'bg-green-100' : 'bg-gray-100'}`}>
                <div className={`w-4 h-4 rounded-full ${hasSalaryRange ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                <span className={`text-sm ${hasSalaryRange ? 'text-green-800' : 'text-gray-600'}`}>Salary</span>
              </div>
            </div>
          </div>
        </ModernCard>
      ) : (
        <ModernCard variant="success">
          <div className="flex items-center gap-3">
            <Award className="w-6 h-6 text-green-600" />
            <div>
              <p className="font-semibold text-green-800">Preferences Complete!</p>
              <p className="text-sm text-green-700 mt-1">
                You're all set for personalized job matching. Ready to find your perfect opportunity!
              </p>
            </div>
          </div>
        </ModernCard>
      )}
    </div>
  );
};

export default JobPreferencesStep;
