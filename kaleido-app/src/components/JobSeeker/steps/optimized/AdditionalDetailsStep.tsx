'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown, Sparkles, TrendingUp } from 'lucide-react';
import React, { useState } from 'react';
import { ModernCard } from '../../components/ModernCard';
import { BaseStepProps } from '../../types';
import { PortfolioStep } from '../PortfolioStep';
import { ValuesStep } from '../ValuesStep';
import { WorkAvailabilityStep } from '../WorkAvailabilityStep';

const AdditionalDetailsStep: React.FC<BaseStepProps> = ({ formData, onUpdate, onNext }) => {
  const [valuesExpanded, setValuesExpanded] = useState(false);
  const [availabilityExpanded, setAvailabilityExpanded] = useState(false);
  const [portfolioExpanded, setPortfolioExpanded] = useState(false);

  const hasValues = formData.myValues?.length > 0;
  const hasAvailability = formData.workAvailability || formData.availability;
  const hasPortfolio =
    formData.portfolio?.length > 0 || formData.linkedIn || formData.github || formData.website;

  const completedSections = [hasValues, hasAvailability, hasPortfolio].filter(Boolean).length;
  const profileBoost = completedSections * 20; // Each section adds 20% boost

  return (
    <div className="space-y-4">
      {/* Compact Progress Indicator */}
      <div className="flex items-center justify-between mb-2">
        <div className="text-xs text-gray-500">{completedSections}/3 optional sections</div>
        {completedSections === 3 && <Sparkles className="w-4 h-4 text-purple-400" />}
      </div>

      {/* Values & Culture - Collapsible */}
      <ModernCard>
        <button
          onClick={() => setValuesExpanded(!valuesExpanded)}
          className="w-full flex items-center justify-between p-3 -m-4 mb-0 bg-gray-50 hover:bg-gray-100 transition-colors rounded-t-xl"
        >
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-900">Values & Culture</span>
            {hasValues && (
              <span className="text-xs text-purple-400">({formData.myValues.length})</span>
            )}
          </div>
          <motion.div animate={{ rotate: valuesExpanded ? 180 : 0 }} transition={{ duration: 0.2 }}>
            <ChevronDown className="w-5 h-5 text-gray-500" />
          </motion.div>
        </button>

        <AnimatePresence>
          {valuesExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-4"
            >
              <div className="space-y-4">
                <ValuesStep {...{ formData, onUpdate, onNext }} />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </ModernCard>

      {/* Availability - Collapsible */}
      <ModernCard>
        <button
          onClick={() => setAvailabilityExpanded(!availabilityExpanded)}
          className="w-full flex items-center justify-between p-3 -m-4 mb-0 bg-gray-50 hover:bg-gray-100 transition-colors rounded-t-xl"
        >
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-900">Work Availability</span>
            {hasAvailability && <span className="text-xs text-purple-400">✓</span>}
          </div>
          <motion.div
            animate={{ rotate: availabilityExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-gray-500" />
          </motion.div>
        </button>

        <AnimatePresence>
          {availabilityExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-4"
            >
              <div className="space-y-4">
                <WorkAvailabilityStep {...{ formData, onUpdate, onNext }} />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </ModernCard>

      {/* Portfolio Links - Collapsible */}
      <ModernCard>
        <button
          onClick={() => setPortfolioExpanded(!portfolioExpanded)}
          className="w-full flex items-center justify-between p-3 -m-4 mb-0 bg-gray-50 hover:bg-gray-100 transition-colors rounded-t-xl"
        >
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-900">Portfolio & Links</span>
            {hasPortfolio && <span className="text-xs text-purple-400">✓</span>}
          </div>
          <motion.div
            animate={{ rotate: portfolioExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-gray-500" />
          </motion.div>
        </button>

        <AnimatePresence>
          {portfolioExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-4"
            >
              <div className="space-y-4">
                <PortfolioStep {...{ formData, onUpdate, onNext }} />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </ModernCard>

      {/* Completion Summary */}
      <ModernCard variant="info">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-blue-100 rounded-full">
            <TrendingUp className="w-6 h-6 text-blue-600" />
          </div>
          <div className="flex-1">
            <h3 className="font-medium text-gray-900">
              Profile Enhancement: +{profileBoost}% Boost
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {completedSections === 3
                ? 'Excellent! Your profile is fully enhanced and will stand out to employers.'
                : completedSections > 0
                  ? `Great progress! Complete ${3 - completedSections} more section${3 - completedSections !== 1 ? 's' : ''} for maximum impact.`
                  : "Add any of these sections to significantly boost your profile's attractiveness to employers."}
            </p>
            <p className="text-xs text-blue-600 mt-2">
              💡 You can always add these details later from your profile settings
            </p>
          </div>
        </div>
      </ModernCard>
    </div>
  );
};

export default AdditionalDetailsStep;
