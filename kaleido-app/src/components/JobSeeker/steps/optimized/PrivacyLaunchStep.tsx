'use client';

import { motion } from 'framer-motion';
import { AlertCircle, CheckCircle, Eye, Lock, Rocket, Users, Shield, Globe, Star, Sparkles } from 'lucide-react';
import React, { useState } from 'react';
import { BaseStepProps } from '../../types';
import { PrivacySettingsStep } from '../PrivacySettingsStep';
import { ModernCard, StatCard } from '../../components/ModernCard';

const PrivacyLaunchStep: React.FC<BaseStepProps> = ({ formData, onUpdate, onNext }) => {
  const [previewMode, setPreviewMode] = useState(false);

  // Calculate profile completion more comprehensively
  const mandatoryComplete = !!(
    formData.firstName &&
    formData.lastName &&
    formData.email &&
    formData.skills?.length > 0 &&
    formData.preferences?.jobTypes?.length > 0 &&
    formData.preferences?.locations?.length > 0 &&
    formData.preferences?.remotePreference &&
    formData.preferences?.desiredSalary?.min &&
    formData.preferences?.desiredSalary?.max
  );

  const optionalSections = {
    summary: { label: 'Professional Summary', complete: !!formData.summary?.trim(), impact: '+15%' },
    experience: { label: 'Work Experience', complete: formData.experience?.length > 0, impact: '+20%' },
    education: { label: 'Education', complete: formData.education?.length > 0, impact: '+10%' },
    values: { label: 'Company Values', complete: formData.myValues?.length > 0, impact: '+15%' },
    portfolio: { label: 'Portfolio & Links', complete: !!(formData.portfolio?.length || formData.linkedIn || formData.github), impact: '+25%' },
    video: { label: 'Video Introduction', complete: !!formData.videoIntroUrl, impact: '+30%' },
    verification: { label: 'ID Verification', complete: !!formData.verifications?.identity?.isVerified, impact: '+20%' },
  };

  const completedOptional = Object.values(optionalSections).filter(item => item.complete).length;
  const totalOptional = Object.keys(optionalSections).length;
  const overallCompletion = mandatoryComplete
    ? Math.round(60 + (completedOptional / totalOptional) * 40)
    : Math.round((completedOptional / totalOptional) * 30);

  // Profile strength categories
  const getProfileStrength = () => {
    if (!mandatoryComplete) return { level: 'Incomplete', color: 'red', description: 'Complete required fields to activate profile' };
    if (overallCompletion >= 90) return { level: 'Exceptional', color: 'green', description: 'Top 5% of profiles - maximum employer visibility' };
    if (overallCompletion >= 80) return { level: 'Excellent', color: 'blue', description: 'Strong profile - high employer interest' };
    if (overallCompletion >= 70) return { level: 'Good', color: 'amber', description: 'Solid profile - good job matching' };
    return { level: 'Basic', color: 'gray', description: 'Meets minimum requirements' };
  };

  const profileStrength = getProfileStrength();

  return (
    <div className="space-y-6">
      {/* Compact Profile Launch Header */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className="text-base font-semibold text-gray-900">
              {mandatoryComplete ? 'Ready to Launch!' : 'Almost There!'}
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {mandatoryComplete ? 'Your profile is ready' : 'Complete required fields to activate'}
            </p>
          </div>
          <div className="text-right">
            <div className={`text-2xl font-bold ${
              profileStrength.color === 'green' ? 'text-green-600' :
              profileStrength.color === 'blue' ? 'text-blue-600' :
              profileStrength.color === 'amber' ? 'text-amber-600' :
              profileStrength.color === 'red' ? 'text-red-600' : 'text-gray-600'
            }`}>
              {overallCompletion}%
            </div>
            <div className="text-xs text-gray-500">Complete</div>
          </div>
        </div>

        {/* Compact Profile Status */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Star className={`w-4 h-4 ${
              profileStrength.color === 'green' ? 'text-green-600' :
              profileStrength.color === 'blue' ? 'text-blue-600' :
              profileStrength.color === 'amber' ? 'text-amber-600' :
              profileStrength.color === 'red' ? 'text-red-600' : 'text-gray-600'
            }`} />
            <span className="text-sm font-medium text-gray-900">{profileStrength.level}</span>
            <span className="text-gray-400">•</span>
            <span className="text-sm text-gray-600">{completedOptional}/{totalOptional} optional</span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${overallCompletion}%` }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className={`h-2 rounded-full ${
              profileStrength.color === 'green' ? 'bg-gradient-to-r from-green-500 to-green-600' :
              profileStrength.color === 'blue' ? 'bg-gradient-to-r from-blue-500 to-blue-600' :
              profileStrength.color === 'amber' ? 'bg-gradient-to-r from-amber-500 to-amber-600' :
              'bg-gradient-to-r from-gray-400 to-gray-500'
            }`}
          />
        </div>
      </div>

      {/* Privacy Settings */}
      <ModernCard title="Privacy & Visibility Settings" subtitle="Control who can see your profile and how">
        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-start gap-3">
              <Lock className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">Your Privacy Matters</h4>
                <p className="text-sm text-blue-700 mt-1">
                  Configure these settings to control your profile visibility and how employers can contact you.
                </p>
              </div>
            </div>
          </div>
          <PrivacySettingsStep {...{ formData, onUpdate, onNext }} />
        </div>
      </ModernCard>

      {/* Section Completion Overview */}
      <ModernCard title="Profile Sections" subtitle="Track your completion progress">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {Object.entries(optionalSections).map(([key, section]) => (
            <div 
              key={key}
              className={`flex items-center justify-between p-3 rounded-lg border ${
                section.complete ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'
              }`}
            >
              <div className="flex items-center gap-2">
                {section.complete ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <div className="w-4 h-4 rounded-full border-2 border-gray-300" />
                )}
                <span className={`text-sm font-medium ${
                  section.complete ? 'text-green-700' : 'text-gray-600'
                }`}>
                  {section.label}
                </span>
              </div>
              <span className={`text-xs px-2 py-1 rounded-full ${
                section.complete ? 'bg-green-100 text-green-700' : 'bg-amber-100 text-amber-700'
              }`}>
                {section.complete ? 'Added' : section.impact}
              </span>
            </div>
          ))}
        </div>
      </ModernCard>

      {/* Profile Preview */}
      <ModernCard title="Profile Preview" subtitle="See how employers will see your profile">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Eye className="w-5 h-5 text-blue-600" />
            <span className="font-medium">Employer View</span>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setPreviewMode(!previewMode)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {previewMode ? 'Hide Preview' : 'Show Preview'}
          </motion.button>
        </div>

        {previewMode && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="border-t pt-4"
          >
            <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl p-6 border">
              <div className="flex items-start gap-6">
                <div className="w-24 h-24 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                  {formData.firstName?.[0]}{formData.lastName?.[0]}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-bold text-xl text-gray-900">
                      {formData.firstName} {formData.lastName}
                    </h4>
                    {overallCompletion >= 80 && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
                        <Star className="w-3 h-3 fill-current" />
                        Premium
                      </div>
                    )}
                  </div>
                  <p className="text-gray-600 mb-3">{formData.email}</p>
                  {formData.summary && (
                    <p className="text-sm text-gray-700 mb-4 bg-white/50 p-3 rounded-lg">
                      {formData.summary}
                    </p>
                  )}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {formData.skills?.slice(0, 6).map((skill: string) => (
                      <span
                        key={skill}
                        className="bg-white/70 text-purple-700 px-3 py-1 rounded-full text-sm font-medium border border-purple-200"
                      >
                        {skill}
                      </span>
                    ))}
                    {formData.skills?.length > 6 && (
                      <span className="bg-gray-200 text-gray-600 px-3 py-1 rounded-full text-sm">
                        +{formData.skills.length - 6} more
                      </span>
                    )}
                  </div>
                  
                  {/* Quick stats */}
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-1">
                      <Users className="w-4 h-4 text-blue-600" />
                      <span className="text-gray-700">{Math.floor(overallCompletion / 10)} connections</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Shield className="w-4 h-4 text-green-600" />
                      <span className="text-gray-700">Verified profile</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Globe className="w-4 h-4 text-purple-600" />
                      <span className="text-gray-700">{formData.location || 'Remote'}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </ModernCard>

      {/* Launch Section */}
      <ModernCard variant={mandatoryComplete ? 'gradient' : 'warning'}>
        <div className="text-center space-y-4">
          <motion.div
            animate={{ 
              rotate: mandatoryComplete ? [0, 5, -5, 0] : 0,
              scale: mandatoryComplete ? [1, 1.1, 1] : 1
            }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <Rocket className={`w-16 h-16 mx-auto mb-4 ${
              mandatoryComplete ? 'text-purple-600' : 'text-gray-400'
            }`} />
          </motion.div>
          
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">
              {mandatoryComplete ? 'Ready for Launch!' : 'Almost There!'}
            </h3>
            <p className="text-gray-600 mb-6">
              {mandatoryComplete 
                ? 'Your profile will be visible to thousands of employers actively hiring'
                : 'Complete all required fields to activate your profile and start connecting with employers'
              }
            </p>
          </div>

          <motion.button
            whileHover={mandatoryComplete ? { scale: 1.05, boxShadow: '0 10px 25px rgba(0,0,0,0.1)' } : {}}
            whileTap={mandatoryComplete ? { scale: 0.98 } : {}}
            onClick={onNext}
            disabled={!mandatoryComplete}
            className={`px-12 py-4 rounded-xl font-bold text-lg transition-all duration-200 ${
              mandatoryComplete
                ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white shadow-lg'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            {mandatoryComplete ? (
              <span className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                Launch My Profile
                <Rocket className="w-5 h-5" />
              </span>
            ) : (
              'Complete Required Fields'
            )}
          </motion.button>
          
          {mandatoryComplete && (
            <p className="text-sm text-gray-500 mt-4">
              🎉 Join over 50,000 professionals who found their next opportunity through our platform
            </p>
          )}
        </div>
      </ModernCard>

      {/* Additional Tips */}
      {mandatoryComplete && completedOptional < totalOptional && (
        <ModernCard variant="info">
          <div className="text-center">
            <h4 className="font-semibold text-blue-900 mb-2">💡 Profile Enhancement Tips</h4>
            <p className="text-sm text-blue-700 mb-3">
              You can boost your profile strength by {Math.round((totalOptional - completedOptional) * 5.7)}% by completing the remaining sections.
            </p>
            <p className="text-xs text-blue-600">
              Remember: You can always add more details later from your profile settings.
            </p>
          </div>
        </ModernCard>
      )}
    </div>
  );
};

export default PrivacyLaunchStep;
