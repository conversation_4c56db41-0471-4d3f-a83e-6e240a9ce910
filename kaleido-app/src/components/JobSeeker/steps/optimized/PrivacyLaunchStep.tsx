'use client';

import { motion } from 'framer-motion';
import { AlertCircle, CheckCircle, Eye, Lock, Rocket, Users, Shield, Globe, Star, Sparkles } from 'lucide-react';
import React, { useState } from 'react';
import { BaseStepProps } from '../../types';
import { PrivacySettingsStep } from '../PrivacySettingsStep';
import { ModernCard, StatCard } from '../../components/ModernCard';

const PrivacyLaunchStep: React.FC<BaseStepProps> = ({ formData, onUpdate, onNext }) => {
  const [previewMode, setPreviewMode] = useState(false);

  // Calculate profile completion more comprehensively
  const mandatoryComplete = !!(
    formData.firstName &&
    formData.lastName &&
    formData.email &&
    formData.skills?.length > 0 &&
    formData.preferences?.jobTypes?.length > 0 &&
    formData.preferences?.locations?.length > 0 &&
    formData.preferences?.remotePreference &&
    formData.preferences?.desiredSalary?.min &&
    formData.preferences?.desiredSalary?.max
  );

  const optionalSections = {
    summary: { label: 'Professional Summary', complete: !!formData.summary?.trim(), impact: '+15%' },
    experience: { label: 'Work Experience', complete: formData.experience?.length > 0, impact: '+20%' },
    education: { label: 'Education', complete: formData.education?.length > 0, impact: '+10%' },
    values: { label: 'Company Values', complete: formData.myValues?.length > 0, impact: '+15%' },
    portfolio: { label: 'Portfolio & Links', complete: !!(formData.portfolio?.length || formData.linkedIn || formData.github), impact: '+25%' },
    video: { label: 'Video Introduction', complete: !!formData.videoIntroUrl, impact: '+30%' },
    verification: { label: 'ID Verification', complete: !!formData.verifications?.identity?.isVerified, impact: '+20%' },
  };

  const completedOptional = Object.values(optionalSections).filter(item => item.complete).length;
  const totalOptional = Object.keys(optionalSections).length;
  const overallCompletion = mandatoryComplete
    ? Math.round(60 + (completedOptional / totalOptional) * 40)
    : Math.round((completedOptional / totalOptional) * 30);

  // Profile strength categories
  const getProfileStrength = () => {
    if (!mandatoryComplete) return { level: 'Incomplete', color: 'red', description: 'Complete required fields to activate profile' };
    if (overallCompletion >= 90) return { level: 'Exceptional', color: 'purple', description: 'Top 5% of profiles - maximum employer visibility' };
    if (overallCompletion >= 80) return { level: 'Excellent', color: 'indigo', description: 'Strong profile - high employer interest' };
    if (overallCompletion >= 70) return { level: 'Good', color: 'amber', description: 'Solid profile - good job matching' };
    return { level: 'Basic', color: 'gray', description: 'Meets minimum requirements' };
  };

  const profileStrength = getProfileStrength();

  return (
    <div className="space-y-4">
      {/* Compact Status */}
      <div className="flex items-center justify-between mb-2">
        <div className="text-xs text-gray-500">
          {mandatoryComplete ? 'Profile ready' : 'Complete required fields'}
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs font-medium text-purple-600">{overallCompletion}%</span>
          {overallCompletion === 100 && (
            <Sparkles className="w-4 h-4 text-purple-400" />
          )}
        </div>
      </div>

      {/* Simple Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-1.5 mb-4">
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: `${overallCompletion}%` }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className={`h-1.5 rounded-full ${
              overallCompletion === 100 ? 'bg-gradient-to-r from-purple-500 to-pink-500' :
              overallCompletion >= 80 ? 'bg-gradient-to-r from-purple-400 to-purple-500' :
              overallCompletion >= 60 ? 'bg-gradient-to-r from-indigo-400 to-purple-400' :
              'bg-gray-300'
            }`}
          />
        </div>
      </div>

      {/* Privacy Settings */}
      <ModernCard title="Privacy Settings">
        <PrivacySettingsStep {...{ formData, onUpdate, onNext }} />
      </ModernCard>

      {/* Section Completion Overview */}
      <ModernCard title="Profile Summary">
        <div className="grid grid-cols-2 gap-2">
          {Object.entries(optionalSections).map(([key, section]) => (
            <div 
              key={key}
              className={`flex items-center gap-2 p-2 rounded-lg ${
                section.complete ? 'bg-purple-50' : 'bg-gray-50'
              }`}
            >
              {section.complete ? (
                <CheckCircle className="w-3 h-3 text-purple-400" />
              ) : (
                <div className="w-3 h-3 rounded-full border border-gray-300" />
              )}
              <span className={`text-xs ${
                section.complete ? 'text-purple-700' : 'text-gray-500'
                }`}>
                  {section.label}
                </span>
            </div>
          ))}
        </div>
      </ModernCard>


      {/* Launch Section */}
      <ModernCard>
        <div className="text-center space-y-3">
          <Rocket className={`w-12 h-12 mx-auto ${
            mandatoryComplete ? 'text-purple-400' : 'text-gray-400'
          }`} />
          
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              {mandatoryComplete ? 'Ready for Launch!' : 'Almost There!'}
            </h3>
            <p className="text-xs text-gray-500">
              {mandatoryComplete 
                ? 'Your profile is ready to launch'
                : 'Complete required fields to continue'
              }
            </p>
          </div>
        </div>
      </ModernCard>

      {/* Additional Tips */}
      {mandatoryComplete && completedOptional < totalOptional && (
        <ModernCard variant="info">
          <div className="text-center">
            <h4 className="font-semibold text-indigo-900 mb-2">Profile Enhancement Tips</h4>
            <p className="text-sm text-indigo-700 mb-3">
              Boost your profile by {Math.round((totalOptional - completedOptional) * 5.7)}%
            </p>
            <p className="text-xs text-indigo-600">
              Complete remaining sections anytime from settings
            </p>
          </div>
        </ModernCard>
      )}
    </div>
  );
};

export default PrivacyLaunchStep;
