'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronUp, User, Mail, Phone, MapPin, Link, UserCheck, Globe, Briefcase, Star, TrendingUp, CheckCircle } from 'lucide-react';
import { BaseStepProps } from '../../types';
import { ModernCard, StatCard } from '../../components/ModernCard';
import { ModernFormInput } from '../../components/ModernFormInput';

const EssentialInfoStep: React.FC<BaseStepProps> = ({ formData, onUpdate, onNext }) => {
  const [additionalInfoExpanded, setAdditionalInfoExpanded] = useState(false);

  // Validation functions
  const validateEmail = (email: string) => {
    if (!email) return 'Email is required';
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) ? null : 'Please enter a valid email address';
  };

  const validateName = (name: string) => {
    if (!name?.trim()) return 'This field is required';
    if (name.trim().length < 2) return 'Must be at least 2 characters';
    return null;
  };

  const validatePhone = (phone: string) => {
    if (!phone) return null; // Optional field
    const phoneRegex = /^[\+]?[1-9][\d\s\-\(\)]{7,}$/;
    return phoneRegex.test(phone.replace(/\s/g, '')) ? null : 'Please enter a valid phone number';
  };

  // Check completion status
  const isFirstNameValid = !validateName(formData.firstName || '');
  const isLastNameValid = !validateName(formData.lastName || '');
  const isEmailValid = !validateEmail(formData.email || '');
  const allMandatoryComplete = isFirstNameValid && isLastNameValid && isEmailValid;

  const optionalFields = [
    { field: 'phone', label: 'Phone Number' },
    { field: 'location', label: 'Location' },
    { field: 'linkedIn', label: 'LinkedIn Profile' },
    { field: 'website', label: 'Personal Website' },
  ];
  
  const completedOptional = optionalFields.filter(f => formData[f.field]).length;

  return (
    <div className="space-y-6">
      {/* Compact Progress Indicator */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          {allMandatoryComplete ? (
            <CheckCircle className="w-4 h-4 text-purple-400" />
          ) : (
            <div className="text-xs text-gray-500">{[isFirstNameValid, isLastNameValid, isEmailValid].filter(Boolean).length}/3 required</div>
          )}
        </div>
        {completedOptional > 0 && (
          <span className="text-xs text-purple-400">{completedOptional} optional added</span>
        )}
      </div>

      {/* Required Information */}
      <ModernCard 
        variant="required" 
        title="Required Information" 
        required={true}
        completed={allMandatoryComplete}
      >
        <div className="space-y-4">

          <div className="grid gap-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ModernFormInput
                label="First Name"
                value={formData.firstName || ''}
                onChange={(value) => onUpdate({ firstName: value })}
                placeholder="Enter your first name"
                required
                validation={validateName}
                prefix={<User className="w-4 h-4" />}
              />
              <ModernFormInput
                label="Last Name"
                value={formData.lastName || ''}
                onChange={(value) => onUpdate({ lastName: value })}
                placeholder="Enter your last name"
                required
                validation={validateName}
                prefix={<User className="w-4 h-4" />}
              />
            </div>
            <ModernFormInput
              label="Email Address"
              type="email"
              value={formData.email || ''}
              onChange={(value) => onUpdate({ email: value })}
              placeholder="<EMAIL>"
              required
              validation={validateEmail}
              prefix={<Mail className="w-4 h-4" />}
            />
          </div>
        </div>
      </ModernCard>

      {/* Optional Information - Collapsible */}
      <ModernCard>
        <button
          onClick={() => setAdditionalInfoExpanded(!additionalInfoExpanded)}
          className="w-full flex items-center justify-between p-3 -m-6 mb-0 bg-gray-50 hover:bg-gray-100 transition-colors rounded-t-xl"
        >
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-900">
              Additional Information
            </span>
            <span className="text-xs text-purple-400">
              Optional {completedOptional > 0 && `(${completedOptional}/4)`}
            </span>
          </div>
          <motion.div
            animate={{ rotate: additionalInfoExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-gray-500" />
          </motion.div>
        </button>

        <AnimatePresence>
          {additionalInfoExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-4"
            >
              <div className="space-y-4">
                <div className="grid gap-4">
                  <ModernFormInput
                    label="Phone Number"
                    type="tel"
                    value={formData.phone || ''}
                    onChange={(value) => onUpdate({ phone: value })}
                    placeholder="+****************"
                    validation={validatePhone}
                    prefix={<Phone className="w-4 h-4" />}
                  />
                  
                  <ModernFormInput
                    label="Location"
                    value={formData.location || ''}
                    onChange={(value) => onUpdate({ location: value })}
                    placeholder="City, State/Country"
                    prefix={<MapPin className="w-4 h-4" />}
                  />
                  
                  <ModernFormInput
                    label="LinkedIn"
                    type="url"
                    value={formData.linkedIn || ''}
                    onChange={(value) => onUpdate({ linkedIn: value })}
                    placeholder="linkedin.com/in/yourprofile"
                    prefix={<Link className="w-4 h-4" />}
                  />
                  
                  <ModernFormInput
                    label="Website"
                    type="url"
                    value={formData.website || ''}
                    onChange={(value) => onUpdate({ website: value })}
                    placeholder="yourwebsite.com"
                    prefix={<Globe className="w-4 h-4" />}
                  />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </ModernCard>
    </div>
  );
};

export default EssentialInfoStep;