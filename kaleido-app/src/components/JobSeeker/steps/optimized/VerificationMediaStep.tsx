'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { Award, Camera, ChevronDown, ChevronUp, Shield, SkipFor<PERSON>, Star, TrendingUp, Eye } from 'lucide-react';
import React, { useState } from 'react';
import { BaseStepProps } from '../../types';
import { IdVerificationStep } from '../IdVerificationStep';
import { ModernCard, StatCard } from '../../components/ModernCard';

const VerificationMediaStep: React.FC<BaseStepProps> = ({ formData, onUpdate, onNext }) => {
  const [videoExpanded, setVideoExpanded] = useState(false);
  const [idExpanded, setIdExpanded] = useState(false);

  const hasVideo = !!formData.videoIntroUrl || !!formData.videoIntroduction;
  const hasId = !!formData.verifications?.identity?.isVerified || !!formData.idVerification;
  
  const trustScore = (hasVideo ? 50 : 0) + (hasId ? 50 : 0);
  const completedVerifications = [hasVideo, hasId].filter(Boolean).length;

  return (
    <div className="space-y-4">
      {/* Compact Progress Indicator */}
      <div className="flex items-center justify-between mb-2">
        <div className="text-xs text-gray-500">{completedVerifications}/2 verifications</div>
        {completedVerifications === 2 && (
          <Shield className="w-4 h-4 text-purple-400" />
        )}
      </div>

      {/* Video Introduction - Collapsible */}
      <ModernCard>
        <button
          onClick={() => setVideoExpanded(!videoExpanded)}
          className="w-full flex items-center justify-between p-3 -m-4 mb-0 bg-gray-50 hover:bg-gray-100 transition-colors rounded-t-xl"
        >
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-900">Video Introduction</span>
            {hasVideo && (
              <span className="text-xs text-purple-400">
                ✓
              </span>
            )}
          </div>
          <motion.div
            animate={{ rotate: videoExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-gray-500" />
          </motion.div>
        </button>

        <AnimatePresence>
          {videoExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-4"
            >
              <div className="space-y-4">
                {/* Benefits */}
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-blue-50 p-3 rounded-lg text-center">
                    <Eye className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                    <div className="text-sm font-medium text-blue-900">2x More Views</div>
                    <div className="text-xs text-blue-700">Stand out from text profiles</div>
                  </div>
                  <div className="bg-purple-50 p-3 rounded-lg text-center">
                    <Award className="w-5 h-5 text-purple-600 mx-auto mb-1" />
                    <div className="text-sm font-medium text-purple-900">Personal Touch</div>
                    <div className="text-xs text-purple-700">Show your communication style</div>
                  </div>
                </div>

                {/* Video Upload/Record UI */}
                <div className="border-2 border-dashed border-blue-300 bg-blue-50 rounded-xl p-8 text-center">
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <Camera className="w-12 h-12 text-blue-500 mx-auto mb-3" />
                  </motion.div>
                  <h4 className="font-semibold text-gray-900 mb-2">Record Your Introduction</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    30-60 seconds • MP4 or MOV format • Max 50MB
                  </p>
                  <div className="flex gap-3 justify-center">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                    >
                      🎥 Record Now
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      📁 Upload File
                    </motion.button>
                  </div>
                </div>

              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </ModernCard>

      {/* ID Verification - Collapsible */}
      <ModernCard>
        <button
          onClick={() => setIdExpanded(!idExpanded)}
          className="w-full flex items-center justify-between p-3 -m-4 mb-0 bg-gray-50 hover:bg-gray-100 transition-colors rounded-t-xl"
        >
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-900">ID Verification</span>
            {hasId && (
              <span className="text-xs text-purple-400">
                ✓
              </span>
            )}
          </div>
          <motion.div
            animate={{ rotate: idExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-gray-500" />
          </motion.div>
        </button>

        <AnimatePresence>
          {idExpanded && (
            <motion.div
              initial={{ height: 0 }}
              animate={{ height: 'auto' }}
              exit={{ height: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="pt-4">
                <IdVerificationStep {...{ formData, onUpdate, onNext }} />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </ModernCard>

      {/* Verification Impact Summary */}
      <ModernCard variant={completedVerifications > 0 ? 'success' : 'info'}>
        <div className="flex items-center gap-4">
          <div className={`p-3 rounded-full ${
            completedVerifications === 2 ? 'bg-green-100' :
            completedVerifications === 1 ? 'bg-amber-100' : 'bg-gray-100'
          }`}>
            <Shield className={`w-6 h-6 ${
              completedVerifications === 2 ? 'text-green-600' :
              completedVerifications === 1 ? 'text-amber-600' : 'text-gray-500'
            }`} />
          </div>
          <div className="flex-1">
            <h3 className="font-medium text-gray-900">
              {completedVerifications === 2 ? 'Fully Verified Profile' :
               completedVerifications === 1 ? 'Partially Verified Profile' :
               'Unverified Profile'}
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {completedVerifications === 2 
                ? "Outstanding! You'll get maximum visibility and trust from employers."
                : completedVerifications === 1
                  ? "Great start! Consider adding the other verification for maximum impact."
                  : "Add verifications to significantly boost employer confidence and response rates."
              }
            </p>
            {completedVerifications < 2 && (
              <div className="mt-2 flex items-center gap-4 text-sm">
                <span className="text-blue-600 font-medium">
                  Potential Impact: +{(2 - completedVerifications) * 40}% more responses
                </span>
              </div>
            )}
          </div>
          <div className="text-right">
            <div className={`text-2xl font-bold ${
              completedVerifications === 2 ? 'text-green-600' :
              completedVerifications === 1 ? 'text-amber-600' : 'text-gray-500'
            }`}>
              {trustScore}%
            </div>
            <div className="text-xs text-gray-500">Trust Score</div>
          </div>
        </div>
      </ModernCard>
    </div>
  );
};

export default VerificationMediaStep;
