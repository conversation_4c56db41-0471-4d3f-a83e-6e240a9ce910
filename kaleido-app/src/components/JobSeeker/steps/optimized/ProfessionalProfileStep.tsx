'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown, ChevronUp, Briefcase, GraduationCap, FileText, Award, X, Zap, Target, TrendingUp, Users, Brain, Rocket, CheckCircle2 } from 'lucide-react';
import React, { useState } from 'react';
import { BaseStepProps } from '../../types';
import { EducationStep } from '../EducationStep';
import { ExperienceStep } from '../ExperienceStep';
import { ProfessionalSummaryStep } from '../ProfessionalSummaryStep';
import { ModernCard, StatCard } from '../../components/ModernCard';
// import { ModernSkillInput } from '../../components/ModernSkillInput';
import { ModernTextArea } from '../../components/ModernFormInput';

const ProfessionalProfileStep: React.FC<BaseStepProps> = ({ formData, onUpdate, onNext }) => {
  const [summaryExpanded, setSummaryExpanded] = useState(false);
  const [experienceExpanded, setExperienceExpanded] = useState(false);
  const [educationExpanded, setEducationExpanded] = useState(false);

  const skills = formData.skills || [];
  const hasRequiredSkills = skills.length > 0;
  const hasSummary = !!formData.summary?.trim();
  const hasExperience = formData.experience?.length > 0;
  const hasEducation = formData.education?.length > 0;

  const optionalItems = [
    { field: 'summary', label: 'Professional Summary', complete: hasSummary },
    { field: 'experience', label: 'Work Experience', complete: hasExperience },
    { field: 'education', label: 'Education', complete: hasEducation },
  ];
  
  const completedOptional = optionalItems.filter(item => item.complete).length;

  return (
    <div className="space-y-6">
      {/* Compact Progress Indicator */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          {hasRequiredSkills ? (
            <CheckCircle2 className="w-4 h-4 text-purple-400" />
          ) : (
            <div className="text-xs text-gray-500">Skills required</div>
          )}
        </div>
        {completedOptional > 0 && (
          <span className="text-xs text-purple-400">{completedOptional} optional added</span>
        )}
      </div>

      {/* Skills Section - Required */}
      <ModernCard 
        variant="required" 
        title="Skills" 
        required={true}
        completed={hasRequiredSkills}
      >
        <div className="space-y-4">
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">
              Add your skills
            </label>
              
              {/* Skills Display */}
              <div className="flex flex-wrap gap-2">
                {skills.map((skill: string, index: number) => (
                  <motion.span
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-gray-100 text-gray-700 rounded-full text-sm font-medium"
                  >
                    {skill}
                    <button
                      onClick={() => onUpdate({ skills: skills.filter((_, i) => i !== index) })}
                      className="hover:bg-gray-200 rounded-full p-0.5 ml-1"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </motion.span>
                ))}
              </div>
              
              {/* Add Skills Input */}
              <div className="flex gap-3">
                <input
                  type="text"
                  placeholder="Type a skill and press Enter (e.g., JavaScript, Project Management)"
                  className="flex-1 px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      const value = (e.target as HTMLInputElement).value.trim();
                      if (value && !skills.includes(value)) {
                        onUpdate({ skills: [...skills, value] });
                        (e.target as HTMLInputElement).value = '';
                      }
                    }
                  }}
                />
                <button
                  onClick={(e) => {
                    const input = (e.target as HTMLButtonElement).previousElementSibling as HTMLInputElement;
                    const value = input.value.trim();
                    if (value && !skills.includes(value)) {
                      onUpdate({ skills: [...skills, value] });
                      input.value = '';
                    }
                  }}
                  className="px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all"
                >
                  <Rocket className="w-4 h-4" />
                </button>
              </div>
              
              {!hasRequiredSkills && (
                <div className="flex items-center gap-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <Award className="w-4 h-4 text-amber-600" />
                  <p className="text-sm text-amber-800">Add at least one skill to showcase your expertise</p>
                </div>
              )}
              
              {skills.length > 0 && (
                <div className="flex items-center gap-2 text-sm text-purple-700">
                  <Users className="w-4 h-4" />
                  <span>{skills.length} skill{skills.length !== 1 ? 's' : ''} added • Great start!</span>
                </div>
              )}
          </div>
        </div>
      </ModernCard>

      {/* Optional Professional Summary - Collapsible */}
      <ModernCard>
        <button
          onClick={() => setSummaryExpanded(!summaryExpanded)}
          className="w-full flex items-center justify-between p-4 -m-6 mb-0 bg-gray-50 hover:bg-gray-100 transition-colors rounded-t-xl"
        >
          <div className="flex items-center gap-3">
            <FileText className="w-5 h-5 text-gray-500" />
            <span className="font-medium text-gray-900">Professional Summary</span>
            <span className="text-sm px-2 py-1 bg-blue-100 text-blue-600 rounded-full">Optional</span>
            {hasSummary && (
              <span className="text-sm text-green-600">✓ Added</span>
            )}
          </div>
          <motion.div
            animate={{ rotate: summaryExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-gray-500" />
          </motion.div>
        </button>

        <AnimatePresence>
          {summaryExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-6"
            >
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg border border-blue-200">
                  <TrendingUp className="w-5 h-5 text-blue-600" />
                  <div>
                    <div className="font-semibold text-blue-900">+30% Profile Views</div>
                    <div className="text-sm text-blue-700">With a compelling professional summary</div>
                  </div>
                </div>
                
                <ModernTextArea
                  label="Professional Summary"
                  value={formData.summary || ''}
                  onChange={(value) => onUpdate({ summary: value })}
                  placeholder="Write a brief summary of your professional background, key skills, and career goals..."
                  rows={4}
                  maxLength={500}
                  hint="2-3 sentences that highlight your experience and what you're looking for"
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </ModernCard>

      {/* Optional Work Experience - Collapsible */}
      <ModernCard>
        <button
          onClick={() => setExperienceExpanded(!experienceExpanded)}
          className="w-full flex items-center justify-between p-4 -m-6 mb-0 bg-gray-50 hover:bg-gray-100 transition-colors rounded-t-xl"
        >
          <div className="flex items-center gap-3">
            <Briefcase className="w-5 h-5 text-gray-500" />
            <span className="font-medium text-gray-900">Work Experience</span>
            <span className="text-sm px-2 py-1 bg-green-100 text-green-600 rounded-full">Optional</span>
            {hasExperience && (
              <span className="text-sm text-purple-600">
                {formData.experience.length} position{formData.experience.length !== 1 ? 's' : ''}
              </span>
            )}
          </div>
          <motion.div
            animate={{ rotate: experienceExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-gray-500" />
          </motion.div>
        </button>

        <AnimatePresence>
          {experienceExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-6"
            >
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
                  <Briefcase className="w-5 h-5 text-green-600" />
                  <div>
                    <div className="font-semibold text-green-900">Career Progression</div>
                    <div className="text-sm text-green-700">Show practical skills and professional growth</div>
                  </div>
                </div>
                <ExperienceStep {...{ formData, onUpdate, onNext }} />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </ModernCard>

      {/* Optional Education - Collapsible */}
      <ModernCard>
        <button
          onClick={() => setEducationExpanded(!educationExpanded)}
          className="w-full flex items-center justify-between p-4 -m-6 mb-0 bg-gray-50 hover:bg-gray-100 transition-colors rounded-t-xl"
        >
          <div className="flex items-center gap-3">
            <GraduationCap className="w-5 h-5 text-gray-500" />
            <span className="font-medium text-gray-900">Education</span>
            <span className="text-sm px-2 py-1 bg-amber-100 text-amber-600 rounded-full">Optional</span>
            {hasEducation && (
              <span className="text-sm text-purple-600">
                {formData.education.length} qualification{formData.education.length !== 1 ? 's' : ''}
              </span>
            )}
          </div>
          <motion.div
            animate={{ rotate: educationExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-gray-500" />
          </motion.div>
        </button>

        <AnimatePresence>
          {educationExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-6"
            >
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg border border-amber-200">
                  <GraduationCap className="w-5 h-5 text-amber-600" />
                  <div>
                    <div className="font-semibold text-amber-900">Academic Foundation</div>
                    <div className="text-sm text-amber-700">Showcase qualifications and knowledge base</div>
                  </div>
                </div>
                <EducationStep {...{ formData, onUpdate, onNext }} />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </ModernCard>

    </div>
  );
};

export default ProfessionalProfileStep;
