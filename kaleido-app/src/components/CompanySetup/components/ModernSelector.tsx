'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, ChevronDown, X, Search } from 'lucide-react';

interface Option {
  value: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
}

interface ModernSelectorProps {
  label: string;
  options: Option[] | string[];
  value?: string;
  selectedValues?: string[];
  onChange: (value: string | string[]) => void;
  placeholder?: string;
  required?: boolean;
  multiple?: boolean;
  searchable?: boolean;
  maxSelections?: number;
  error?: string;
  hint?: string;
  className?: string;
  disabled?: boolean;
}

export const ModernSelector: React.FC<ModernSelectorProps> = ({
  label,
  options,
  value,
  selectedValues = [],
  onChange,
  placeholder = 'Select option...',
  required = false,
  multiple = false,
  searchable = false,
  maxSelections,
  error,
  hint,
  className = '',
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const normalizedOptions: Option[] = options.map((opt) => 
    typeof opt === 'string' ? { value: opt, label: opt } : opt
  );

  const filteredOptions = searchable
    ? normalizedOptions.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        option.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : normalizedOptions;

  const currentValues = multiple ? selectedValues : (value ? [value] : []);
  const selectedOptions = normalizedOptions.filter(option => currentValues.includes(option.value));

  const handleSelect = (option: Option) => {
    if (multiple) {
      if (currentValues.includes(option.value)) {
        onChange(currentValues.filter(v => v !== option.value));
      } else {
        if (maxSelections && currentValues.length >= maxSelections) {
          return;
        }
        onChange([...currentValues, option.value]);
      }
    } else {
      onChange(option.value);
      setIsOpen(false);
    }
  };

  const removeSelection = (val: string, e?: React.MouseEvent) => {
    e?.stopPropagation();
    if (multiple) {
      onChange(currentValues.filter(v => v !== val));
    } else {
      onChange('');
    }
  };

  return (
    <div className={`relative ${className}`}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
        {required && <span className="text-pink-500 ml-1">*</span>}
        {maxSelections && multiple && (
          <span className="text-gray-400 ml-2 text-xs">
            ({currentValues.length}/{maxSelections})
          </span>
        )}
      </label>

      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`w-full min-h-[48px] px-3 py-2 border rounded-lg text-left transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-pink-500 ${
          error
            ? 'border-red-500 bg-red-50'
            : isOpen
            ? 'border-pink-500 ring-2 ring-pink-100'
            : 'border-gray-300 hover:border-gray-400 bg-white'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            {selectedOptions.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {(multiple ? selectedOptions.slice(0, 3) : selectedOptions).map((option) => (
                  <span
                    key={option.value}
                    className="inline-flex items-center gap-1 px-2 py-1 bg-pink-100 text-pink-700 rounded-md text-sm"
                  >
                    {option.icon}
                    <span>{option.label}</span>
                    {(multiple || !multiple) && (
                      <button
                        onClick={(e) => removeSelection(option.value, e)}
                        className="ml-1 hover:bg-pink-200 rounded-full p-0.5 transition-colors"
                        disabled={disabled}
                      >
                        <X className="w-3 h-3" />
                      </button>
                    )}
                  </span>
                ))}
                {multiple && selectedOptions.length > 3 && (
                  <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-md text-sm">
                    +{selectedOptions.length - 3} more
                  </span>
                )}
              </div>
            ) : (
              <span className="text-gray-400">{placeholder}</span>
            )}
          </div>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-gray-400" />
          </motion.div>
        </div>
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.15 }}
            className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-64 overflow-hidden"
          >
            {searchable && (
              <div className="p-3 border-b border-gray-100">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search options..."
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:border-pink-500"
                  />
                </div>
              </div>
            )}

            <div className="max-h-48 overflow-y-auto">
              {filteredOptions.length > 0 ? (
                filteredOptions.map((option) => {
                  const isSelected = currentValues.includes(option.value);
                  const isDisabled = maxSelections && !isSelected && currentValues.length >= maxSelections;

                  return (
                    <button
                      key={option.value}
                      onClick={() => !isDisabled && handleSelect(option)}
                      disabled={isDisabled}
                      className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center gap-3 ${
                        isSelected ? 'bg-pink-50 text-pink-900' : 'text-gray-900'
                      } ${
                        isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                      }`}
                    >
                      <div className="flex-shrink-0">
                        {multiple ? (
                          isSelected ? (
                            <div className="w-5 h-5 bg-pink-500 rounded flex items-center justify-center">
                              <Check className="w-3 h-3 text-white" />
                            </div>
                          ) : (
                            <div className="w-5 h-5 border-2 border-gray-300 rounded" />
                          )
                        ) : (
                          isSelected && (
                            <Check className="w-4 h-4 text-pink-600" />
                          )
                        )}
                      </div>
                      <div className="flex items-center gap-2 flex-1">
                        {option.icon}
                        <div>
                          <div className="font-medium">{option.label}</div>
                          {option.description && (
                            <div className="text-sm text-gray-500">{option.description}</div>
                          )}
                        </div>
                      </div>
                    </button>
                  );
                })
              ) : (
                <div className="px-4 py-3 text-center text-gray-500">
                  {searchTerm ? 'No options found' : 'No options available'}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {(error || hint) && (
        <div className="mt-2">
          {error ? (
            <p className="text-sm text-red-600">{error}</p>
          ) : hint ? (
            <p className="text-sm text-gray-500">{hint}</p>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default ModernSelector;